# -*- coding: utf-8 -*-
"""
نظام النسخ الاحتياطي التلقائي المتقدم
Advanced Automatic Backup System
"""

import os
import shutil
import sqlite3
import gzip
import json
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Optional
import hashlib

# استيراد اختياري لمكتبة schedule
try:
    import schedule
    SCHEDULE_AVAILABLE = True
except ImportError:
    SCHEDULE_AVAILABLE = False
    print("⚠️ مكتبة schedule غير متاحة - النسخ الاحتياطي التلقائي معطل")

from config import BackupConfig, AppConfig, DatabaseConfig
from logging_config import smart_logger

class BackupManager:
    """مدير النسخ الاحتياطي المتقدم"""
    
    def __init__(self):
        self.backup_thread = None
        self.is_running = False
        self.backup_history = []
        self.load_backup_history()
        
    def load_backup_history(self):
        """تحميل تاريخ النسخ الاحتياطي"""
        try:
            history_file = AppConfig.BACKUP_DIR / "backup_history.json"
            if history_file.exists():
                with open(history_file, 'r', encoding='utf-8') as f:
                    self.backup_history = json.load(f)
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في تحميل تاريخ النسخ الاحتياطي")
            self.backup_history = []
    
    def save_backup_history(self):
        """حفظ تاريخ النسخ الاحتياطي"""
        try:
            history_file = AppConfig.BACKUP_DIR / "backup_history.json"
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(self.backup_history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في حفظ تاريخ النسخ الاحتياطي")
    
    def create_backup(self, backup_type="full", compress=True) -> Dict:
        """إنشاء نسخة احتياطية"""
        try:
            smart_logger.log_info(f"بدء إنشاء نسخة احتياطية من نوع: {backup_type}")
            
            # إنشاء مجلد النسخ الاحتياطي
            AppConfig.BACKUP_DIR.mkdir(exist_ok=True)
            
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = BackupConfig.BACKUP_FILENAME_FORMAT.format(timestamp=timestamp)
            
            if compress:
                backup_filename += ".gz"
            
            backup_path = AppConfig.BACKUP_DIR / backup_filename
            
            # إنشاء النسخة الاحتياطية
            if backup_type == "full":
                success = self._create_full_backup(backup_path, compress)
            elif backup_type == "incremental":
                success = self._create_incremental_backup(backup_path, compress)
            else:
                success = self._create_differential_backup(backup_path, compress)
            
            if success:
                # حساب معلومات النسخة الاحتياطية
                backup_info = self._get_backup_info(backup_path, backup_type)
                
                # إضافة إلى التاريخ
                self.backup_history.append(backup_info)
                self.save_backup_history()
                
                # تنظيف النسخ القديمة
                self._cleanup_old_backups()
                
                smart_logger.log_info(f"تم إنشاء النسخة الاحتياطية بنجاح: {backup_filename}")
                return backup_info
            else:
                smart_logger.log_error("فشل في إنشاء النسخة الاحتياطية")
                return None
                
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في إنشاء النسخة الاحتياطية")
            return None
    
    def _create_full_backup(self, backup_path: Path, compress: bool) -> bool:
        """إنشاء نسخة احتياطية كاملة"""
        try:
            source_db = DatabaseConfig.DB_PATH
            
            if not source_db.exists():
                smart_logger.log_error("ملف قاعدة البيانات غير موجود")
                return False
            
            if compress:
                # ضغط النسخة الاحتياطية
                with open(source_db, 'rb') as f_in:
                    with gzip.open(backup_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
            else:
                # نسخ مباشر
                shutil.copy2(source_db, backup_path)
            
            return True
            
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في إنشاء النسخة الاحتياطية الكاملة")
            return False
    
    def _create_incremental_backup(self, backup_path: Path, compress: bool) -> bool:
        """إنشاء نسخة احتياطية تزايدية"""
        # للتبسيط، سنستخدم النسخة الكاملة
        # يمكن تطوير هذا لاحقاً لدعم النسخ التزايدية الحقيقية
        return self._create_full_backup(backup_path, compress)
    
    def _create_differential_backup(self, backup_path: Path, compress: bool) -> bool:
        """إنشاء نسخة احتياطية تفاضلية"""
        # للتبسيط، سنستخدم النسخة الكاملة
        # يمكن تطوير هذا لاحقاً لدعم النسخ التفاضلية الحقيقية
        return self._create_full_backup(backup_path, compress)
    
    def _get_backup_info(self, backup_path: Path, backup_type: str) -> Dict:
        """الحصول على معلومات النسخة الاحتياطية"""
        try:
            stat = backup_path.stat()
            
            # حساب الهاش للتحقق من سلامة الملف
            file_hash = self._calculate_file_hash(backup_path)
            
            return {
                "filename": backup_path.name,
                "path": str(backup_path),
                "type": backup_type,
                "size": stat.st_size,
                "created_at": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "hash": file_hash,
                "compressed": backup_path.suffix == ".gz"
            }
            
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في الحصول على معلومات النسخة الاحتياطية")
            return {}
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """حساب هاش الملف للتحقق من سلامته"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""
    
    def _cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            if len(self.backup_history) <= BackupConfig.MAX_BACKUP_FILES:
                return
            
            # ترتيب النسخ حسب التاريخ
            sorted_backups = sorted(self.backup_history, key=lambda x: x.get('created_at', ''))
            
            # حذف النسخ الزائدة
            backups_to_delete = sorted_backups[:-BackupConfig.MAX_BACKUP_FILES]
            
            for backup in backups_to_delete:
                try:
                    backup_path = Path(backup['path'])
                    if backup_path.exists():
                        backup_path.unlink()
                        smart_logger.log_info(f"تم حذف النسخة الاحتياطية القديمة: {backup['filename']}")
                    
                    self.backup_history.remove(backup)
                    
                except Exception as e:
                    smart_logger.log_exception(e, f"خطأ في حذف النسخة الاحتياطية: {backup['filename']}")
            
            self.save_backup_history()
            
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في تنظيف النسخ الاحتياطية القديمة")
    
    def restore_backup(self, backup_path: str) -> bool:
        """استعادة نسخة احتياطية"""
        try:
            smart_logger.log_info(f"بدء استعادة النسخة الاحتياطية: {backup_path}")
            
            backup_file = Path(backup_path)
            if not backup_file.exists():
                smart_logger.log_error("ملف النسخة الاحتياطية غير موجود")
                return False
            
            # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
            current_backup = self.create_backup("full", compress=True)
            if current_backup:
                smart_logger.log_info("تم إنشاء نسخة احتياطية من قاعدة البيانات الحالية")
            
            # استعادة النسخة الاحتياطية
            target_db = DatabaseConfig.DB_PATH
            
            if backup_file.suffix == ".gz":
                # فك ضغط النسخة الاحتياطية
                with gzip.open(backup_file, 'rb') as f_in:
                    with open(target_db, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
            else:
                # نسخ مباشر
                shutil.copy2(backup_file, target_db)
            
            smart_logger.log_info("تم استعادة النسخة الاحتياطية بنجاح")
            return True
            
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في استعادة النسخة الاحتياطية")
            return False
    
    def verify_backup(self, backup_path: str) -> bool:
        """التحقق من سلامة النسخة الاحتياطية"""
        try:
            backup_file = Path(backup_path)
            if not backup_file.exists():
                return False
            
            # البحث عن معلومات النسخة في التاريخ
            backup_info = None
            for backup in self.backup_history:
                if backup['path'] == backup_path:
                    backup_info = backup
                    break
            
            if not backup_info:
                smart_logger.log_warning(f"لم يتم العثور على معلومات النسخة الاحتياطية: {backup_path}")
                return False
            
            # التحقق من الهاش
            current_hash = self._calculate_file_hash(backup_file)
            if current_hash != backup_info.get('hash', ''):
                smart_logger.log_error(f"فشل التحقق من سلامة النسخة الاحتياطية: {backup_path}")
                return False
            
            # محاولة فتح قاعدة البيانات للتحقق من صحتها
            if backup_info.get('compressed'):
                # فك ضغط مؤقت للتحقق
                import tempfile
                with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as temp_file:
                    with gzip.open(backup_file, 'rb') as f_in:
                        shutil.copyfileobj(f_in, temp_file)
                    
                    temp_path = temp_file.name
                
                try:
                    conn = sqlite3.connect(temp_path)
                    conn.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    conn.close()
                    os.unlink(temp_path)
                    return True
                except Exception:
                    if os.path.exists(temp_path):
                        os.unlink(temp_path)
                    return False
            else:
                try:
                    conn = sqlite3.connect(backup_file)
                    conn.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    conn.close()
                    return True
                except Exception:
                    return False
                    
        except Exception as e:
            smart_logger.log_exception(e, "خطأ في التحقق من النسخة الاحتياطية")
            return False
    
    def get_backup_list(self) -> List[Dict]:
        """الحصول على قائمة النسخ الاحتياطية"""
        # تحديث القائمة بالملفات الموجودة فعلياً
        existing_backups = []
        for backup in self.backup_history:
            if Path(backup['path']).exists():
                existing_backups.append(backup)
        
        self.backup_history = existing_backups
        self.save_backup_history()
        
        return sorted(self.backup_history, key=lambda x: x.get('created_at', ''), reverse=True)
    
    def start_automatic_backup(self):
        """بدء النسخ الاحتياطي التلقائي"""
        if not BackupConfig.AUTO_BACKUP_ENABLED:
            return

        if not SCHEDULE_AVAILABLE:
            smart_logger.log_info("النسخ الاحتياطي التلقائي معطل - مكتبة schedule غير متاحة")
            return

        try:
            # جدولة النسخ الاحتياطي
            schedule.every(BackupConfig.BACKUP_INTERVAL_HOURS).hours.do(
                lambda: self.create_backup("full", compress=BackupConfig.BACKUP_COMPRESSION)
            )

            # جدولة النسخ اليومي
            schedule.every().day.at(BackupConfig.BACKUP_TIME_DAILY).do(
                lambda: self.create_backup("full", compress=BackupConfig.BACKUP_COMPRESSION)
            )

            # بدء خيط النسخ الاحتياطي
            self.is_running = True
            self.backup_thread = threading.Thread(target=self._backup_scheduler, daemon=True)
            self.backup_thread.start()

            smart_logger.log_info("تم بدء النسخ الاحتياطي التلقائي")

        except Exception as e:
            smart_logger.log_exception(e, "خطأ في بدء النسخ الاحتياطي التلقائي")
    
    def stop_automatic_backup(self):
        """إيقاف النسخ الاحتياطي التلقائي"""
        self.is_running = False
        if SCHEDULE_AVAILABLE:
            schedule.clear()
        smart_logger.log_info("تم إيقاف النسخ الاحتياطي التلقائي")
    
    def _backup_scheduler(self):
        """مجدول النسخ الاحتياطي"""
        if not SCHEDULE_AVAILABLE:
            return

        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # فحص كل دقيقة
            except Exception as e:
                smart_logger.log_exception(e, "خطأ في مجدول النسخ الاحتياطي")
                time.sleep(300)  # انتظار 5 دقائق عند حدوث خطأ

# إنشاء مثيل عام من مدير النسخ الاحتياطي
backup_manager = BackupManager()

if __name__ == "__main__":
    # اختبار نظام النسخ الاحتياطي
    print("🔧 اختبار نظام النسخ الاحتياطي...")
    
    # إنشاء نسخة احتياطية تجريبية
    backup_info = backup_manager.create_backup("full", compress=True)
    if backup_info:
        print(f"✅ تم إنشاء النسخة الاحتياطية: {backup_info['filename']}")
        
        # التحقق من النسخة الاحتياطية
        if backup_manager.verify_backup(backup_info['path']):
            print("✅ تم التحقق من سلامة النسخة الاحتياطية")
        else:
            print("❌ فشل التحقق من النسخة الاحتياطية")
    else:
        print("❌ فشل في إنشاء النسخة الاحتياطية")
    
    # عرض قائمة النسخ الاحتياطية
    backups = backup_manager.get_backup_list()
    print(f"📋 عدد النسخ الاحتياطية المتاحة: {len(backups)}")
    
    print("✅ تم اختبار نظام النسخ الاحتياطي بنجاح")
